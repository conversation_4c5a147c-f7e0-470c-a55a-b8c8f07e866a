<template>
  <el-drawer title="发送邮件" :visible.sync="drawerVisible" size="80%">
    <div v-loading="loading">
      <DynamicForm
        ref="baseForm"
        :config="config"
        :params="formParams"
        labelPosition="right"
        :defaultColSpan="24"
        labelWidth="150px"
      >
        <template #mailReplyContent>
          <Editor
            v-model="formParams.mailReplyContent"
            ref="quillEditor"
            class="email-editor"
          />
        </template>
      </DynamicForm>
      <div class="drawer-btn">
        <el-button @click.stop="drawerVisible = false" size="medium"
          >取 消</el-button
        >
        <el-button
          @click="handleSubmit"
          type="primary"
          size="medium"
          :loading="btnLoading"
          >确 定</el-button
        >
      </div>
    </div>
  </el-drawer>
</template>

<script>
import api from "@/api/ledger/commonEmail.js";
import { initParams } from "@/utils/buse.js";
import Editor from "@/components/Editor/index.vue";
import { queryTreeList } from "@/api/ledger/businessType.js";

export default {
  components: { Editor },
  data() {
    return {
      baseData: {},
      formParams: {},
      drawerVisible: false,
      btnLoading: false,
      loading: false,
      // 邮箱来源列表
      emailSourceOptions: [],
      // 业务类型树形数据
      businessTypeOptions: [],
    };
  },
  computed: {
    config() {
      return [
        {
          field: "isReplyNew",
          title: "是否按新邮件发送",
          element: "el-radio-group",
          props: {
            options: [
              { value: "N", label: "否（按回复方式发送，将携带原始邮件内容）" },
              { value: "Y", label: "是（按新邮件发送，不带原始邮件内容）" },
            ],
          },
          rules: [{ required: true, trigger: "change", message: "请选择" }],
          defaultValue: "N",
          on: {
            change: (val) => {
              this.formParams.mailReplyContent =
                val === "Y" ? this.baseData.mailContent : "";
            },
          },
        },
        {
          field: "emailSubject",
          title: "邮件主题",
          attrs: {
            maxlength: 1000,
          },
          rules: [
            {
              required: true,
              trigger: "change",
              message: "邮件主题不能为空",
            },
          ],
          defaultValue: this.baseData.mailSubject,
        },
        {
          field: "recipient",
          title: "收件人",
          attrs: {
            placeholder: "请输入邮件地址，多个地址之间用【,】分隔",
          },
          rules: [
            {
              required: true,
              message: "收件人邮箱地址不能为空",
            },
            { validator: this.validateEmails },
          ],
          defaultValue: this.baseData.sender?.match(/<([^>]+)>/)?.[1] || "",
        },
        {
          field: "ccRecipient",
          title: "抄送人",
          attrs: {
            placeholder: "请输入邮件地址，多个地址之间用【,】分隔",
          },
          rules: [{ validator: this.validateEmails }],
          defaultValue: this.baseData.ccTo,
        },
        // {
        //   field: "isReplied",
        //   title: "是否邮件回复",
        //   element: "el-radio-group",
        //   props: {
        //     options: [
        //       { value: "Y", label: "回复" },
        //       {
        //         value: "N",
        //         label: "不回复（不发送邮件，仅在维保通内记录配置结果）",
        //       },
        //     ],
        //   },
        //   rules: [{ required: true, trigger: "change", message: "请选择" }],
        //   defaultValue: "Y",
        // },
        {
          field: "mailReplyContent",
          title: "邮件回复内容",
          slotName: "mailReplyContent",
          element: "slot",
          rules: [
            {
              required: true,
              trigger: "change",
              message: "邮件回复内容不能为空",
            },
          ],
        },

        {
          field: "sender",
          title: "发件人",
          element: "el-select",
          props: {
            filterable: true, // 支持搜索
            placeholder: "请选择或输入关键词搜索",
            options: this.emailSourceOptions,
          },
          rules: [
            {
              required: true,
              message: "请选择发件人",
              trigger: "change",
            },
          ],
        },

        {
          field: "isCreateOrder",
          title: "是否创建工单台账",
          element: "el-radio-group",
          props: {
            options: [
              { value: "N", label: "不创建工单" },
              {
                value: "Y",
                label: "创建工单（仅自动生成结算采购计划书相关工单）",
              },
            ],
          },
          defaultValue: "N",
          on: {
            change: (val) => {
              // 当选择不创建工单时，清空业务类型
              if (val === "N") {
                this.formParams.businessType = undefined;
              }
            },
          },
        },

        {
          field: "businessType",
          title: "业务类型",
          element: "custom-cascader",
          attrs: {
            collapseTags: true,
            props: {
              checkStrictly: true,
              multiple: false,
              value: "id",
              label: "typeName",
              children: "childrenList",
            },
            filterable: true,
            placeholder: "请选择业务类型",
            options: this.businessTypeOptions,
          },
          rules: [
            {
              required: this.formParams.isCreateOrder === "Y",
              message: "请选择业务类型",
              trigger: "change",
            },
          ],
          // 条件显示：只有当选择创建工单时才显示
          show: this.formParams.isCreateOrder === "Y",
        },
        {
          field: "sendAttachmentFileList",
          title: "上传",
          element: "file-upload",
          props: {
            limit: 99999,
            accept: ".jpg, .jpeg, .png, .doc, .docx, .xls, .xlsx, .pdf",
            fileMaxSize: 50,
            textTip:
              "支持批量上传，支持word、excel、pdf、图片格式的文件，单个文件50M以内",
          },
          defaultValue: this.baseData.attachment || [],
        },
      ];
    },
  },
  created() {
    this.formParams = initParams(this.config);
    // 获取邮箱来源列表
    this.getEmailSources();
    // 获取业务类型列表
    this.getBusinessTypes();
  },
  methods: {
    // 获取邮箱来源列表
    getEmailSources() {
      this.loading = true;
      // 使用邮件数据源配置接口获取邮箱列表
      api
        .getMailSourceConfigList({
          pageSize: 9999,
          pageNum: 1,
          status: "01", // 只获取启用状态的邮箱
        })
        .then((res) => {
          if (res && res.success) {
            // 将返回的数据转换为下拉选项格式
            this.emailSourceOptions = (res.data || []).map((item) => ({
              value: item.id, // 使用邮箱配置ID作为值
              label: item.mailAddr, // 显示邮箱地址
            }));
          }
        })
        .catch((error) => {
          console.error("获取邮箱来源列表失败", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 获取业务类型列表
    getBusinessTypes() {
      this.loading = true;
      // 使用业务类型接口获取树形数据
      queryTreeList({})
        .then((res) => {
          if (res && res.data) {
            this.businessTypeOptions = res.data;
          }
        })
        .catch((error) => {
          console.error("获取业务类型列表失败", error);
        })
        .finally(() => {
          this.loading = false;
        });
    },

    // 邮箱校验
    validateEmails(rule, value, callback) {
      const emails = value?.split(/,\s*/) || [];
      const invalid = emails.find(
        (e) => !/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(e)
      );
      invalid ? callback(new Error(`无效邮箱地址：${invalid}`)) : callback();
    },
    handleSubmit() {
      this.$refs.baseForm.validate((valid) => {
        if (!valid) return false;
        let params = {
          ...this.formParams,
          mailReplyContent: this.$refs.quillEditor.fontClassToStyle(
            this.formParams.mailReplyContent
          ),
          mailDataId: this.baseData.mailId,
          messageId: this.baseData.messageId,
          // 添加新的请求参数
          sender: this.formParams.sender || "", // 发件人（邮箱数据源配置id）
          isCreateOrder: this.formParams.isCreateOrder || "N", // 是否创建工单台账，Y：是，N：否
          oneBusinessTypeId:
            this.formParams.isCreateOrder === "Y"
              ? this.getBusinessTypeIdByLevel(this.formParams.businessType, 1)
              : undefined, // 一级业务类型id
          twoBusinessTypeId:
            this.formParams.isCreateOrder === "Y"
              ? this.getBusinessTypeIdByLevel(this.formParams.businessType, 2)
              : undefined, // 二级业务类型id
          threeBusinessTypeId:
            this.formParams.isCreateOrder === "Y"
              ? this.getBusinessTypeIdByLevel(this.formParams.businessType, 3)
              : undefined, // 三级业务类型id
        };
        this.btnLoading = true;
        api
          .replyEmail(params)
          .then((res) => {
            this.$message.success("发送邮件成功");
            this.handleClose();
          })
          .finally(() => {
            this.btnLoading = false;
          });
      });
    },

    // 根据业务类型ID和级别获取对应级别的业务类型ID
    getBusinessTypeIdByLevel(businessTypeArr, level) {
      if (!businessTypeArr || !businessTypeArr.length) return undefined;

      // 级联选择器返回的是一个数组，包含了选择路径上的所有ID
      // 例如：[一级ID, 二级ID, 三级ID]
      // 根据级别返回对应的ID
      if (level === 1 && businessTypeArr.length >= 1) {
        return businessTypeArr[0]; // 返回一级业务类型ID
      } else if (level === 2 && businessTypeArr.length >= 2) {
        return businessTypeArr[1]; // 返回二级业务类型ID
      } else if (level === 3 && businessTypeArr.length >= 3) {
        return businessTypeArr[2]; // 返回三级业务类型ID
      }

      return undefined;
    },
    handleClose() {
      this.drawerVisible = false;
      this.$emit("close");
    },
    open(row) {
      this.getEmailSources();
      this.baseData = { ...row };
      this.drawerVisible = true;
      this.formParams = initParams(this.config);
    },
  },
};
</script>

<style lang="less" scoped>
.drawer-btn {
  display: flex;
  justify-content: center;
  .el-button + .el-button {
    margin-left: 40px;
  }
  .el-button--medium {
    font-size: 16px;
    border-radius: 4px;
    padding: 14px 26px;
  }
}
.email-editor {
  height: 550px;
  /deep/ .ql-container {
    max-height: 480px;
    height: 480px;
  }
}
</style>
