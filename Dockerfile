FROM registry.cn-hangzhou.aliyuncs.com/leo_library/node:14.17.1 as build
WORKDIR /tmp
COPY . .
## 安装依赖
RUN npm install --legacy-peer-deps
RUN npm config set strict-ssl false
## 打包 按照自己实际打包命令修改
RUN npm run build:stage
## 拉取nginx镜像
FROM registry.cn-hangzhou.aliyuncs.com/leo_library/nginx:1.12.2
WORKDIR /usr/share/nginx/html/charging-maintenance-ui
RUN rm -f *
COPY --from=build /tmp/dist .
#本地工程中修改nginx 配置
COPY --from=build /tmp/nginx.conf /etc/nginx/nginx.conf